-- Server-side Hauptskript
-- <PERSON>ses Skript läuft nur auf dem Server

print("🚀 Server gestartet!")
print("✨ Live-Synchronisation mit VSCode aktiv!")

-- Beispiel: Spieler-Events
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Event für Client-Server Kommunikation
local remoteEvent = Instance.new("RemoteEvent")
remoteEvent.Name = "PlayerMessage"
remoteEvent.Parent = ReplicatedStorage

-- Wenn ein Spieler dem Spiel beitritt
Players.PlayerAdded:Connect(function(player)
    print("Spieler beigetreten:", player.Name)
    
    -- Willkommensnachricht an alle Spieler senden
    remoteEvent:FireAllClients("Spieler " .. player.Name .. " ist dem Spiel beigetreten!")
end)

-- Wenn ein Spieler das Spiel verlässt
Players.PlayerRemoving:Connect(function(player)
    print("Spieler verlassen:", player.Name)
    remoteEvent:FireAllClients("Spieler " .. player.Name .. " hat das Spiel verlassen!")
end)

-- Event Handler für Client-Nachrichten
remoteEvent.OnServerEvent:Connect(function(player, message)
    print("Nachricht von", player.Name .. ":", message)
    -- Nachricht an alle anderen Spieler weiterleiten
    remoteEvent:FireAllClients(player.Name .. ": " .. message)
end)
