<roblox xmlns:xmime="http://www.w3.org/2005/05/xmlmime" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.roblox.com/roblox.xsd" version="4">
	<External>null</External>
	<External>nil</External>
	<Item class="Workspace" referent="11">
		<Properties>
			<float name="AirDensity">0.00120000006</float>
			<bool name="AllowThirdPartySales">false</bool>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<token name="AuthorityMode">1</token>
			<token name="AvatarUnificationMode">0</token>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<token name="ClientAnimatorThrottling">0</token>
			<BinaryString name="CollisionGroupData">AQEABP////8HRGVmYXVsdA==</BinaryString>
			<Ref name="CurrentCamera">RBX7092574E1727496FB0E4D5734AA0EF5D</Ref>
			<bool name="DefinesCapabilities">false</bool>
			<double name="DistributedGameTime">0</double>
			<bool name="ExplicitAutoJoints">true</bool>
			<bool name="FallHeightEnabled">true</bool>
			<float name="FallenPartsDestroyHeight">-500</float>
			<token name="FluidForces">0</token>
			<Vector3 name="GlobalWind">
				<X>0</X>
				<Y>0</Y>
				<Z>0</Z>
			</Vector3>
			<float name="Gravity">196.199997</float>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<token name="IKControlConstraintSupport">0</token>
			<token name="LevelOfDetail">0</token>
			<token name="LuauTypeCheckMode">0</token>
			<token name="MeshPartHeadsAndAccessories">0</token>
			<CoordinateFrame name="ModelMeshCFrame">
				<X>0</X>
				<Y>0</Y>
				<Z>0</Z>
				<R00>1</R00>
				<R01>0</R01>
				<R02>0</R02>
				<R10>0</R10>
				<R11>1</R11>
				<R12>0</R12>
				<R20>0</R20>
				<R21>0</R21>
				<R22>1</R22>
			</CoordinateFrame>
			<SharedString name="ModelMeshData">yuZpQdnvvUBOTYh1jqZ2cA==</SharedString>
			<Vector3 name="ModelMeshSize">
				<X>0</X>
				<Y>0</Y>
				<Z>0</Z>
			</Vector3>
			<token name="ModelStreamingBehavior">0</token>
			<token name="ModelStreamingMode">0</token>
			<token name="MoverConstraintRootBehavior">0</token>
			<string name="Name">Workspace</string>
			<bool name="NeedsPivotMigration">false</bool>
			<token name="PathfindingUseImprovedSearch">0</token>
			<token name="PhysicsImprovedSleep">0</token>
			<token name="PhysicsSteppingMethod">0</token>
			<token name="PlayerCharacterDestroyBehavior">0</token>
			<token name="PrimalPhysicsSolver">0</token>
			<Ref name="PrimaryPart">null</Ref>
			<token name="RejectCharacterDeletions">0</token>
			<token name="RenderingCacheOptimizations">0</token>
			<token name="ReplicateInstanceDestroySetting">0</token>
			<token name="Retargeting">0</token>
			<token name="SandboxedInstanceMode">0</token>
			<float name="ScaleFactor">1</float>
			<token name="SignalBehavior2">0</token>
			<int64 name="SourceAssetId">-1</int64>
			<token name="StreamOutBehavior">0</token>
			<bool name="StreamingEnabled">false</bool>
			<token name="StreamingIntegrityMode">0</token>
			<int name="StreamingMinRadius">64</int>
			<int name="StreamingTargetRadius">1024</int>
			<BinaryString name="Tags"></BinaryString>
			<bool name="TerrainWeldsFixed">true</bool>
			<token name="TouchEventsUseCollisionGroups">0</token>
			<bool name="TouchesUseCollisionGroups">false</bool>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000002</UniqueId>
			<token name="UseImprovedModelLod">0</token>
			<token name="UseNewLuauTypeSolver">0</token>
			<OptionalCoordinateFrame name="WorldPivotData"></OptionalCoordinateFrame>
		</Properties>
		<Item class="Camera" referent="RBX7092574E1727496FB0E4D5734AA0EF5D">
			<Properties>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<CoordinateFrame name="CFrame">
					<X>-1.01625311</X>
					<Y>13.3070679</Y>
					<Z>24.1013756</Z>
					<R00>0.999390841</R00>
					<R01>0.0145058781</R01>
					<R02>-0.0317424014</R02>
					<R10>-9.31322464e-10</R10>
					<R11>0.909528017</R11>
					<R12>0.415642828</R12>
					<R20>0.0348998606</R20>
					<R21>-0.415389627</R21>
					<R22>0.908973932</R22>
				</CoordinateFrame>
				<Ref name="CameraSubject">null</Ref>
				<token name="CameraType">0</token>
				<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
				<bool name="DefinesCapabilities">false</bool>
				<float name="FieldOfView">70</float>
				<token name="FieldOfViewMode">0</token>
				<CoordinateFrame name="Focus">
					<X>0</X>
					<Y>0</Y>
					<Z>-5</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="HeadLocked">true</bool>
				<float name="HeadScale">1</float>
				<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
				<string name="Name">Camera</string>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<UniqueId name="UniqueId">18ccb430d92583bb0877168400000368</UniqueId>
				<bool name="VRTiltAndRollEnabled">false</bool>
			</Properties>
		</Item>
		<Item class="Part" referent="12">
			<Properties>
				<bool name="Anchored">true</bool>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<bool name="AudioCanCollide">true</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">4</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<bool name="CanQuery">true</bool>
				<bool name="CanTouch">true</bool>
				<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
				<bool name="CastShadow">true</bool>
				<string name="CollisionGroup">Default</string>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4284702563</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<bool name="DefinesCapabilities">false</bool>
				<bool name="EnableFluidForces">true</bool>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<bool name="Massless">false</bool>
				<token name="Material">256</token>
				<string name="MaterialVariantSerialized"></string>
				<string name="Name">Baseplate</string>
				<CoordinateFrame name="PivotOffset">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<int name="RootPriority">0</int>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<UniqueId name="UniqueId">18ccb430d92583bb0877168400000395</UniqueId>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">1</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>512</X>
					<Y>20</Y>
					<Z>512</Z>
				</Vector3>
			</Properties>
		</Item>
		<Item class="Terrain" referent="RBXAED4B3A7014D4522AD3DA5D6D7CADE65">
			<Properties>
				<token name="AcquisitionMethod">0</token>
				<bool name="Anchored">true</bool>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<bool name="AudioCanCollide">true</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">4</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<bool name="CanQuery">true</bool>
				<bool name="CanTouch">true</bool>
				<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
				<bool name="CastShadow">true</bool>
				<string name="CollisionGroup">Default</string>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4288914085</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<bool name="Decoration">false</bool>
				<bool name="DefinesCapabilities">false</bool>
				<bool name="EnableFluidForces">true</bool>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="GrassLength">0.699999988</float>
				<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<bool name="Massless">false</bool>
				<token name="Material">256</token>
				<BinaryString name="MaterialColors"><![CDATA[AAAAAAAAan8/P39rf2Y/ilY+j35fi21PZmxvZbDqw8faiVpHOi4kHh4lZlw76JxKc3trhHta
gcLgc4RKxr21zq2UlJSM]]></BinaryString>
				<string name="MaterialVariantSerialized"></string>
				<string name="Name">Terrain</string>
				<BinaryString name="PhysicsGrid">AgMAAAAAAAAAAAAAAAA=</BinaryString>
				<CoordinateFrame name="PivotOffset">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<int name="RootPriority">0</int>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="SmoothGrid">AQU=</BinaryString>
				<bool name="SmoothVoxelsUpgraded">false</bool>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<UniqueId name="UniqueId">18ccb430d92583bb0877168400000396</UniqueId>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<Color3 name="WaterColor">
					<R>0.0500000007</R>
					<G>0.330000013</G>
					<B>0.360000014</B>
				</Color3>
				<float name="WaterReflectance">1</float>
				<float name="WaterTransparency">0.300000012</float>
				<float name="WaterWaveSize">0.150000006</float>
				<float name="WaterWaveSpeed">10</float>
				<Vector3 name="size">
					<X>2044</X>
					<Y>252</Y>
					<Z>2044</Z>
				</Vector3>
			</Properties>
		</Item>
	</Item>
	<Item class="TimerService" referent="RBX3AE898C80E704B018F0E472E99D0AC5A">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">TimerService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000321</UniqueId>
		</Properties>
	</Item>
	<Item class="SoundService" referent="7">
		<Properties>
			<token name="AmbientReverb">0</token>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<token name="AudioApiByDefault">0</token>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<token name="CharacterSoundsUseNewApi">0</token>
			<token name="DefaultListenerLocation">0</token>
			<bool name="DefinesCapabilities">false</bool>
			<float name="DistanceFactor">3.32999992</float>
			<float name="DopplerScale">1</float>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<bool name="IsNewExpForAudioApiByDefault">false</bool>
			<string name="Name">SoundService</string>
			<bool name="RespectFilteringEnabled">true</bool>
			<float name="RolloffScale">1</float>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000322</UniqueId>
			<token name="VolumetricAudio">1</token>
		</Properties>
	</Item>
	<Item class="VideoCaptureService" referent="RBXF973D4297F6146558218A228A1CCAE6C">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">VideoCaptureService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb087716840000032e</UniqueId>
		</Properties>
	</Item>
	<Item class="NonReplicatedCSGDictionaryService" referent="RBX9F5829E8A5654B1BB9BCA18EEA093D01">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">NonReplicatedCSGDictionaryService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb087716840000032f</UniqueId>
		</Properties>
	</Item>
	<Item class="CSGDictionaryService" referent="RBX0A8620574E3941BEB3CB7A20195066F6">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">CSGDictionaryService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000330</UniqueId>
		</Properties>
	</Item>
	<Item class="Chat" referent="RBX4AA3D83F8C4243218CC34BC1A504028E">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<bool name="BubbleChatEnabled">false</bool>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<bool name="IsAutoMigrated">false</bool>
			<bool name="LoadDefaultChat">true</bool>
			<string name="Name">Chat</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000335</UniqueId>
		</Properties>
	</Item>
	<Item class="Players" referent="RBX9E93D8F49E14488980C19CF50C49B4C2">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<bool name="BanningEnabled">true</bool>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="CharacterAutoLoads">true</bool>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<int name="MaxPlayersInternal">12</int>
			<string name="Name">Players</string>
			<int name="PreferredPlayersInternal">0</int>
			<float name="RespawnTime">5</float>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000337</UniqueId>
			<bool name="UseStrafingAnimations">false</bool>
		</Properties>
	</Item>
	<Item class="ReplicatedFirst" referent="RBX9B41E10D690141B6B34BA8F2A4AE2D8D">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">ReplicatedFirst</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb087716840000033a</UniqueId>
		</Properties>
	</Item>
	<Item class="TweenService" referent="RBX9E802BB308BD4022A93DAD935691D0FF">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">TweenService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb087716840000033c</UniqueId>
		</Properties>
	</Item>
	<Item class="MaterialService" referent="RBXFF6BD339447F4B2BAD8F3C6A29FF5449">
		<Properties>
			<string name="AsphaltName">Asphalt</string>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<string name="BasaltName">Basalt</string>
			<string name="BrickName">Brick</string>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<string name="CardboardName">Cardboard</string>
			<string name="CarpetName">Carpet</string>
			<string name="CeramicTilesName">CeramicTiles</string>
			<string name="ClayRoofTilesName">ClayRoofTiles</string>
			<string name="CobblestoneName">Cobblestone</string>
			<string name="ConcreteName">Concrete</string>
			<string name="CorrodedMetalName">CorrodedMetal</string>
			<string name="CrackedLavaName">CrackedLava</string>
			<bool name="DefinesCapabilities">false</bool>
			<string name="DiamondPlateName">DiamondPlate</string>
			<string name="FabricName">Fabric</string>
			<string name="FoilName">Foil</string>
			<string name="GlacierName">Glacier</string>
			<string name="GraniteName">Granite</string>
			<string name="GrassName">Grass</string>
			<string name="GroundName">Ground</string>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="IceName">Ice</string>
			<string name="LeafyGrassName">LeafyGrass</string>
			<string name="LeatherName">Leather</string>
			<string name="LimestoneName">Limestone</string>
			<string name="MarbleName">Marble</string>
			<string name="MetalName">Metal</string>
			<string name="MudName">Mud</string>
			<string name="Name">MaterialService</string>
			<string name="PavementName">Pavement</string>
			<string name="PebbleName">Pebble</string>
			<string name="PlasterName">Plaster</string>
			<string name="PlasticName">Plastic</string>
			<string name="RockName">Rock</string>
			<string name="RoofShinglesName">RoofShingles</string>
			<string name="RubberName">Rubber</string>
			<string name="SaltName">Salt</string>
			<string name="SandName">Sand</string>
			<string name="SandstoneName">Sandstone</string>
			<string name="SlateName">Slate</string>
			<string name="SmoothPlasticName">SmoothPlastic</string>
			<string name="SnowName">Snow</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb087716840000033d</UniqueId>
			<bool name="Use2022MaterialsXml">false</bool>
			<string name="WoodName">Wood</string>
			<string name="WoodPlanksName">WoodPlanks</string>
		</Properties>
	</Item>
	<Item class="TextChatService" referent="RBXFB403514C5FF48998CE306E2079E2F8B">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="ChatTranslationFTUXShown">true</bool>
			<bool name="ChatTranslationToggleEnabled">false</bool>
			<token name="ChatVersion">0</token>
			<bool name="CreateDefaultCommands">true</bool>
			<bool name="CreateDefaultTextChannels">true</bool>
			<bool name="DefinesCapabilities">false</bool>
			<bool name="HasSeenDeprecationDialog">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<bool name="IsLegacyChatDisabled">false</bool>
			<string name="Name">TextChatService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb087716840000033e</UniqueId>
		</Properties>
		<Item class="ChatWindowConfiguration" referent="RBX95E78CD5FA504C5F9DA15FD876DCEDD3">
			<Properties>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<Color3 name="BackgroundColor3">
					<R>0.0980392173</R>
					<G>0.105882354</G>
					<B>0.113725491</B>
				</Color3>
				<double name="BackgroundTransparency">0.2999999999999999889</double>
				<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
				<bool name="DefinesCapabilities">false</bool>
				<bool name="Enabled">true</bool>
				<Font name="FontFace">
					<Family><url>rbxasset://fonts/families/GothamSSm.json</url></Family>
					<Weight>500</Weight>
					<Style>Normal</Style>
					<CachedFaceId><url>rbxasset://fonts/Montserrat-Medium.ttf</url></CachedFaceId>
				</Font>
				<float name="HeightScale">1</float>
				<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
				<token name="HorizontalAlignment">1</token>
				<string name="Name">ChatWindowConfiguration</string>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<Color3 name="TextColor3">
					<R>1</R>
					<G>1</G>
					<B>1</B>
				</Color3>
				<int64 name="TextSize">14</int64>
				<Color3 name="TextStrokeColor3">
					<R>0</R>
					<G>0</G>
					<B>0</B>
				</Color3>
				<double name="TextStrokeTransparency">0.5</double>
				<UniqueId name="UniqueId">18ccb430d92583bb0877168400000398</UniqueId>
				<token name="VerticalAlignment">1</token>
				<float name="WidthScale">1</float>
			</Properties>
		</Item>
		<Item class="ChatInputBarConfiguration" referent="RBX1885BB78727E441CB93330086BD18999">
			<Properties>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<bool name="AutocompleteEnabled">true</bool>
				<Color3 name="BackgroundColor3">
					<R>0.0980392173</R>
					<G>0.105882354</G>
					<B>0.113725491</B>
				</Color3>
				<double name="BackgroundTransparency">0.2000000000000000111</double>
				<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
				<bool name="DefinesCapabilities">false</bool>
				<bool name="Enabled">true</bool>
				<Font name="FontFace">
					<Family><url>rbxasset://fonts/families/GothamSSm.json</url></Family>
					<Weight>500</Weight>
					<Style>Normal</Style>
					<CachedFaceId><url>rbxasset://fonts/Montserrat-Medium.ttf</url></CachedFaceId>
				</Font>
				<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
				<token name="KeyboardKeyCode">47</token>
				<string name="Name">ChatInputBarConfiguration</string>
				<Color3 name="PlaceholderColor3">
					<R>0.698039234</R>
					<G>0.698039234</G>
					<B>0.698039234</B>
				</Color3>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<Ref name="TargetTextChannel">null</Ref>
				<Color3 name="TextColor3">
					<R>1</R>
					<G>1</G>
					<B>1</B>
				</Color3>
				<int64 name="TextSize">14</int64>
				<Color3 name="TextStrokeColor3">
					<R>0</R>
					<G>0</G>
					<B>0</B>
				</Color3>
				<double name="TextStrokeTransparency">0.5</double>
				<UniqueId name="UniqueId">18ccb430d92583bb0877168400000399</UniqueId>
			</Properties>
		</Item>
		<Item class="BubbleChatConfiguration" referent="RBXBFA37FEEAC464976A3FBE2CBC47FAFDF">
			<Properties>
				<string name="AdorneeName">HumanoidRootPart</string>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<Color3 name="BackgroundColor3">
					<R>0.980392158</R>
					<G>0.980392158</G>
					<B>0.980392158</B>
				</Color3>
				<double name="BackgroundTransparency">0.10000000000000000555</double>
				<float name="BubbleDuration">15</float>
				<float name="BubblesSpacing">6</float>
				<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
				<bool name="DefinesCapabilities">false</bool>
				<bool name="Enabled">true</bool>
				<token name="Font">18</token>
				<Font name="FontFace">
					<Family><url>rbxasset://fonts/families/GothamSSm.json</url></Family>
					<Weight>500</Weight>
					<Style>Normal</Style>
					<CachedFaceId><url>rbxasset://fonts/Montserrat-Medium.ttf</url></CachedFaceId>
				</Font>
				<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
				<Vector3 name="LocalPlayerStudsOffset">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<float name="MaxBubbles">3</float>
				<float name="MaxDistance">100</float>
				<float name="MinimizeDistance">40</float>
				<string name="Name">BubbleChatConfiguration</string>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<bool name="TailVisible">true</bool>
				<Color3 name="TextColor3">
					<R>0.223529413</R>
					<G>0.23137255</G>
					<B>0.239215687</B>
				</Color3>
				<int64 name="TextSize">16</int64>
				<UniqueId name="UniqueId">18ccb430d92583bb087716840000039a</UniqueId>
				<float name="VerticalStudsOffset">0</float>
			</Properties>
		</Item>
		<Item class="ChannelTabsConfiguration" referent="RBXDFBC27F33D6647EF9582118DA7BA4FEE">
			<Properties>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<Color3 name="BackgroundColor3">
					<R>0.0980392173</R>
					<G>0.105882354</G>
					<B>0.113725491</B>
				</Color3>
				<double name="BackgroundTransparency">0</double>
				<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
				<bool name="DefinesCapabilities">false</bool>
				<bool name="Enabled">false</bool>
				<Font name="FontFace">
					<Family><url>rbxasset://fonts/families/BuilderSans.json</url></Family>
					<Weight>700</Weight>
					<Style>Normal</Style>
					<CachedFaceId><url>rbxasset://fonts/BuilderSans-Bold.otf</url></CachedFaceId>
				</Font>
				<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
				<Color3 name="HoverBackgroundColor3">
					<R>0.490196079</R>
					<G>0.490196079</G>
					<B>0.490196079</B>
				</Color3>
				<string name="Name">ChannelTabsConfiguration</string>
				<Color3 name="SelectedTabTextColor3">
					<R>1</R>
					<G>1</G>
					<B>1</B>
				</Color3>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<Color3 name="TextColor3">
					<R>0.686274529</R>
					<G>0.686274529</G>
					<B>0.686274529</B>
				</Color3>
				<int64 name="TextSize">18</int64>
				<Color3 name="TextStrokeColor3">
					<R>0</R>
					<G>0</G>
					<B>0</B>
				</Color3>
				<double name="TextStrokeTransparency">1</double>
				<UniqueId name="UniqueId">18ccb430d92583bb087716840000039b</UniqueId>
			</Properties>
		</Item>
	</Item>
	<Item class="PermissionsService" referent="RBXFB77C5ABC74A43329981059AF8DC2F29">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">PermissionsService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000340</UniqueId>
		</Properties>
	</Item>
	<Item class="PlayerEmulatorService" referent="RBX7FB07F8802864EC58DEF3D22BC0B9EB6">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="CustomPoliciesEnabled">false</bool>
			<bool name="DefinesCapabilities">false</bool>
			<string name="EmulatedCountryCode"></string>
			<string name="EmulatedGameLocale"></string>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">PlayerEmulatorService</string>
			<bool name="PlayerEmulationEnabled">false</bool>
			<bool name="PseudolocalizationEnabled">false</bool>
			<BinaryString name="SerializedEmulatedPolicyInfo"></BinaryString>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<int name="TextElongationFactor">0</int>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000341</UniqueId>
		</Properties>
	</Item>
	<Item class="StudioData" referent="RBX87A9A87B163744B78A75178D030574C7">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<bool name="EnableScriptCollabByDefaultOnLoad">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">StudioData</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000345</UniqueId>
		</Properties>
	</Item>
	<Item class="StarterPlayer" referent="8">
		<Properties>
			<bool name="AllowCustomAnimations">true</bool>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<bool name="AutoJumpEnabled">true</bool>
			<token name="AvatarJointUpgrade_SerializedRollout">0</token>
			<float name="CameraMaxZoomDistance">400</float>
			<float name="CameraMinZoomDistance">0.5</float>
			<token name="CameraMode">0</token>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<float name="CharacterJumpHeight">7.19999981</float>
			<float name="CharacterJumpPower">50</float>
			<float name="CharacterMaxSlopeAngle">89</float>
			<bool name="CharacterUseJumpPower">true</bool>
			<float name="CharacterWalkSpeed">16</float>
			<bool name="ClassicDeath">true</bool>
			<bool name="DefinesCapabilities">false</bool>
			<token name="DevCameraOcclusionMode">0</token>
			<token name="DevComputerCameraMovementMode">0</token>
			<token name="DevComputerMovementMode">0</token>
			<token name="DevTouchCameraMovementMode">0</token>
			<token name="DevTouchMovementMode">0</token>
			<token name="EnableDynamicHeads">0</token>
			<bool name="EnableMouseLockOption">true</bool>
			<int64 name="GameSettingsAssetIDFace">0</int64>
			<int64 name="GameSettingsAssetIDHead">0</int64>
			<int64 name="GameSettingsAssetIDLeftArm">0</int64>
			<int64 name="GameSettingsAssetIDLeftLeg">0</int64>
			<int64 name="GameSettingsAssetIDPants">0</int64>
			<int64 name="GameSettingsAssetIDRightArm">0</int64>
			<int64 name="GameSettingsAssetIDRightLeg">0</int64>
			<int64 name="GameSettingsAssetIDShirt">0</int64>
			<int64 name="GameSettingsAssetIDTeeShirt">0</int64>
			<int64 name="GameSettingsAssetIDTorso">0</int64>
			<token name="GameSettingsAvatar">1</token>
			<token name="GameSettingsR15Collision">0</token>
			<NumberRange name="GameSettingsScaleRangeBodyType">0 1 </NumberRange>
			<NumberRange name="GameSettingsScaleRangeHead">0.95 1 </NumberRange>
			<NumberRange name="GameSettingsScaleRangeHeight">0.9 1.05 </NumberRange>
			<NumberRange name="GameSettingsScaleRangeProportion">0 1 </NumberRange>
			<NumberRange name="GameSettingsScaleRangeWidth">0.7 1 </NumberRange>
			<float name="HealthDisplayDistance">100</float>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<bool name="LoadCharacterAppearance">true</bool>
			<token name="LoadCharacterLayeredClothing">0</token>
			<token name="LuaCharacterController">0</token>
			<string name="Name">StarterPlayer</string>
			<float name="NameDisplayDistance">100</float>
			<bool name="RagdollDeath">true</bool>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000347</UniqueId>
			<bool name="UserEmotesEnabled">true</bool>
		</Properties>
		<Item class="StarterPlayerScripts" referent="9">
			<Properties>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
				<bool name="DefinesCapabilities">false</bool>
				<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
				<string name="Name">StarterPlayerScripts</string>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<UniqueId name="UniqueId">18ccb430d92583bb0877168400000393</UniqueId>
			</Properties>
			<Item class="LocalScript" referent="10">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
					<bool name="DefinesCapabilities">false</bool>
					<bool name="Disabled">false</bool>
					<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
					<Content name="LinkedSource"><null></null></Content>
					<string name="Name">Client</string>
					<token name="RunContext">0</token>
					<string name="ScriptGuid">{988F05B1-C30F-4CF8-B6A6-D5E8174AE67C}</string>
					<ProtectedString name="Source">print(&quot;Hello world, from client!&quot;)</ProtectedString>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<UniqueId name="UniqueId">18ccb430d92583bb0877168400000394</UniqueId>
				</Properties>
			</Item>
		</Item>
		<Item class="StarterCharacterScripts" referent="RBXD0AF2E603CD94934A8F098689F48676C">
			<Properties>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
				<bool name="DefinesCapabilities">false</bool>
				<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
				<string name="Name">StarterCharacterScripts</string>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<UniqueId name="UniqueId">18ccb430d92583bb0877168400000397</UniqueId>
			</Properties>
		</Item>
		<Item class="LocalScript" referent="RBXDA05B1284F644DA0BB9D3E7067A06A84">
			<Properties>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
				<bool name="DefinesCapabilities">false</bool>
				<bool name="Disabled">false</bool>
				<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
				<Content name="LinkedSource"><null></null></Content>
				<string name="Name">LocalScript</string>
				<token name="RunContext">0</token>
				<string name="ScriptGuid">{71CF3726-E476-417B-B4C5-AF5C5C626FEE}</string>
				<ProtectedString name="Source"><![CDATA[print("🔴 MANUELLER TEST!")

local Players = game:GetService("Players")
local player = Players.LocalPlayer

wait(2)

local screenGui = Instance.new("ScreenGui")
screenGui.Parent = player.PlayerGui

local frame = Instance.new("Frame")
frame.Size = UDim2.new(1, 0, 1, 0)  -- GANZER BILDSCHIRM!
frame.BackgroundColor3 = Color3.new(1, 0, 0)  -- ROT
frame.Parent = screenGui

local label = Instance.new("TextLabel")
label.Size = UDim2.new(1, 0, 1, 0)
label.BackgroundTransparency = 1
label.Text = "🔴 ROTER BILDSCHIRM - TEST FUNKTIONIERT! 🔴"
label.TextColor3 = Color3.new(1, 1, 1)
label.TextScaled = true
label.Font = Enum.Font.GothamBold
label.Parent = frame

print("✅ Manueller Test erstellt!")]]></ProtectedString>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<UniqueId name="UniqueId">18ccb430d92583bb0877168400005dbd</UniqueId>
			</Properties>
		</Item>
	</Item>
	<Item class="StarterPack" referent="RBX7E68121F919C4BD9B380AA0A73EEC3E7">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">StarterPack</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000348</UniqueId>
		</Properties>
	</Item>
	<Item class="StarterGui" referent="RBXF413696B668C46BFABC8572FB6574F46">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">StarterGui</string>
			<bool name="ResetPlayerGuiOnSpawn">true</bool>
			<token name="RtlTextSupport">0</token>
			<token name="ScreenOrientation">2</token>
			<bool name="ShowDevelopmentGui">true</bool>
			<int64 name="SourceAssetId">-1</int64>
			<Ref name="StudioDefaultStyleSheet">null</Ref>
			<Ref name="StudioInsertWidgetLayerCollectorAutoLinkStyleSheet">null</Ref>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000349</UniqueId>
			<token name="VirtualCursorMode">0</token>
		</Properties>
	</Item>
	<Item class="LocalizationService" referent="RBX6047821F8ACF4FFEA15EDE809D4FD546">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">LocalizationService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb087716840000034b</UniqueId>
		</Properties>
	</Item>
	<Item class="TeleportService" referent="RBX4A7B9537A5E54133B21300E8198BE316">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">Teleport Service</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000350</UniqueId>
		</Properties>
	</Item>
	<Item class="CollectionService" referent="RBX694422C34A6C4FD3A7D0C213C82285D2">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">CollectionService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000352</UniqueId>
		</Properties>
	</Item>
	<Item class="PhysicsService" referent="RBXB1450DE8BB7A4BEA8169A9F289B506FC">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">PhysicsService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000353</UniqueId>
		</Properties>
	</Item>
	<Item class="InsertService" referent="RBXE30D82CDED5741649425C6B67454755C">
		<Properties>
			<bool name="AllowClientInsertModels">false</bool>
			<bool name="AllowInsertFreeModels">false</bool>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">InsertService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000356</UniqueId>
		</Properties>
		<Item class="StringValue" referent="RBXCAFE38ECC7C04425B4296FBD6EDF26BD">
			<Properties>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
				<bool name="DefinesCapabilities">false</bool>
				<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
				<string name="Name">InsertionHash</string>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<UniqueId name="UniqueId">18ccb430d92583bb0877168400005dc1</UniqueId>
				<string name="Value">{3F730FB5-140A-4EC7-80C9-DC995A45CF2C}</string>
			</Properties>
		</Item>
	</Item>
	<Item class="GamePassService" referent="RBX45D46CE36F064806A5F82711E5A5C208">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">GamePassService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000357</UniqueId>
		</Properties>
	</Item>
	<Item class="Debris" referent="RBX0B2954652C2E4EDD8BF7E83F5D36D3E8">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<int name="MaxItems">1000</int>
			<string name="Name">Debris</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000358</UniqueId>
		</Properties>
	</Item>
	<Item class="CookiesService" referent="RBX4FFC966EE05142069B4D099F3B9ADAE0">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">CookiesService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000359</UniqueId>
		</Properties>
	</Item>
	<Item class="Selection" referent="RBXBB84E1B512814BDD876CD20967E45DD3">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">Selection</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb087716840000035a</UniqueId>
		</Properties>
	</Item>
	<Item class="VRService" referent="RBXEBC6326753EA40FF8EF593D3AA1930FD">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<token name="AutomaticScaling">0</token>
			<bool name="AvatarGestures">false</bool>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<token name="ControllerModels">1</token>
			<bool name="DefinesCapabilities">false</bool>
			<bool name="FadeOutViewOnCollision">true</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<token name="LaserPointer">1</token>
			<string name="Name">VRService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb087716840000035e</UniqueId>
		</Properties>
	</Item>
	<Item class="ContextActionService" referent="RBXA1B600C54F9348629CC27695AED96455">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">ContextActionService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb087716840000035f</UniqueId>
		</Properties>
	</Item>
	<Item class="ScriptService" referent="RBXF620ABCB98F44E4C8E5723CFB4133984">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">ScriptService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000360</UniqueId>
		</Properties>
	</Item>
	<Item class="AssetService" referent="RBX757E34588DC347E59F20AB09058030ED">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">AssetService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000361</UniqueId>
		</Properties>
	</Item>
	<Item class="TouchInputService" referent="RBX69E9895BC270428194BC54FDA67C2262">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">TouchInputService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000362</UniqueId>
		</Properties>
	</Item>
	<Item class="LuaWebService" referent="RBX3E021863DBD240B28719DF6676A334A1">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">LuaWebService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb087716840000036f</UniqueId>
		</Properties>
	</Item>
	<Item class="ProcessInstancePhysicsService" referent="RBX331292DAB7EF43138F7EFC488A3E85C9">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">ProcessInstancePhysicsService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000370</UniqueId>
		</Properties>
	</Item>
	<Item class="ReplicatedStorage" referent="1">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">ReplicatedStorage</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000371</UniqueId>
		</Properties>
		<Item class="Folder" referent="2">
			<Properties>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
				<bool name="DefinesCapabilities">false</bool>
				<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
				<string name="Name">Shared</string>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<UniqueId name="UniqueId">18ccb430d92583bb087716840000038e</UniqueId>
			</Properties>
			<Item class="ModuleScript" referent="3">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
					<bool name="DefinesCapabilities">false</bool>
					<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
					<Content name="LinkedSource"><null></null></Content>
					<string name="Name">GameConfig</string>
					<string name="ScriptGuid">{2D86783C-FA1E-4603-BF0B-7A20E680424B}</string>
					<ProtectedString name="Source"><![CDATA[-- Geteilte Konfiguration zwischen Server und Client
-- Diese Datei kann sowohl vom Server als auch vom Client verwendet werden

local GameConfig = {}

-- Spiel-Einstellungen
GameConfig.GAME_NAME = "Mein Roblox Spiel"
GameConfig.VERSION = "1.0.0"

-- Chat-Einstellungen
GameConfig.MAX_MESSAGE_LENGTH = 100
GameConfig.CHAT_COOLDOWN = 1 -- Sekunden zwischen Nachrichten

-- Spieler-Einstellungen
GameConfig.DEFAULT_WALKSPEED = 16
GameConfig.DEFAULT_JUMPPOWER = 50

-- Farben
GameConfig.COLORS = {
    PRIMARY = Color3.new(0.2, 0.6, 1),
    SECONDARY = Color3.new(1, 0.8, 0.2),
    SUCCESS = Color3.new(0.2, 0.8, 0.2),
    ERROR = Color3.new(0.8, 0.2, 0.2),
    WARNING = Color3.new(1, 0.6, 0.2)
}

-- Utility-Funktionen
function GameConfig.formatTime(seconds)
    local minutes = math.floor(seconds / 60)
    local remainingSeconds = seconds % 60
    return string.format("%02d:%02d", minutes, remainingSeconds)
end

function GameConfig.isValidMessage(message)
    return type(message) == "string" 
        and string.len(message) > 0 
        and string.len(message) <= GameConfig.MAX_MESSAGE_LENGTH
end

return GameConfig
]]></ProtectedString>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<UniqueId name="UniqueId">18ccb430d92583bb087716840000038f</UniqueId>
				</Properties>
			</Item>
			<Item class="ModuleScript" referent="4">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
					<bool name="DefinesCapabilities">false</bool>
					<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
					<Content name="LinkedSource"><null></null></Content>
					<string name="Name">Hello</string>
					<string name="ScriptGuid">{54F0FDF5-377B-4F7E-B1FD-2B35787CF495}</string>
					<ProtectedString name="Source"><![CDATA[return function()
	print("Hello, world!")
end]]></ProtectedString>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<UniqueId name="UniqueId">18ccb430d92583bb0877168400000390</UniqueId>
				</Properties>
			</Item>
		</Item>
	</Item>
	<Item class="ServerScriptService" referent="5">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<bool name="LoadStringEnabled">false</bool>
			<string name="Name">ServerScriptService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000372</UniqueId>
		</Properties>
		<Item class="Script" referent="6">
			<Properties>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
				<bool name="DefinesCapabilities">false</bool>
				<bool name="Disabled">false</bool>
				<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
				<Content name="LinkedSource"><null></null></Content>
				<string name="Name">Server</string>
				<token name="RunContext">0</token>
				<string name="ScriptGuid">{C172DD71-4BC5-40D3-9E2E-03F5DEA14A3E}</string>
				<ProtectedString name="Source">print(&quot;Hello world, from server!&quot;)</ProtectedString>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<UniqueId name="UniqueId">18ccb430d92583bb0877168400000391</UniqueId>
			</Properties>
		</Item>
	</Item>
	<Item class="ServerStorage" referent="RBX11F83C70922447DCA242B10F9D51E7D3">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">ServerStorage</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000373</UniqueId>
		</Properties>
	</Item>
	<Item class="ServiceVisibilityService" referent="RBX8CC02B29B4994FD9BAA90352E8D59B2D">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<BinaryString name="HiddenServices">AAAAAA==</BinaryString>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">ServiceVisibilityService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400000377</UniqueId>
			<BinaryString name="VisibleServices">AAAAAA==</BinaryString>
		</Properties>
	</Item>
	<Item class="HttpService" referent="RBX2C3F354591AA411FBE65B324FE008718">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<bool name="HttpEnabled">false</bool>
			<string name="Name">HttpService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb087716840000038b</UniqueId>
		</Properties>
	</Item>
	<Item class="Lighting" referent="0">
		<Properties>
			<Color3 name="Ambient">
				<R>0</R>
				<G>0</G>
				<B>0</B>
			</Color3>
			<BinaryString name="AttributesSerialize"><![CDATA[AgAAACYAAABSQlhfTGlnaHRpbmdUZWNobm9sb2d5VW5pZmllZE1pZ3JhdGlvbgMAIAAAAFJC
WF9PcmlnaW5hbFRlY2hub2xvZ3lPbkZpbGVMb2FkBAEAAAA=]]></BinaryString>
			<float name="Brightness">2</float>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<Color3 name="ColorShift_Bottom">
				<R>0</R>
				<G>0</G>
				<B>0</B>
			</Color3>
			<Color3 name="ColorShift_Top">
				<R>0</R>
				<G>0</G>
				<B>0</B>
			</Color3>
			<bool name="DefinesCapabilities">false</bool>
			<float name="EnvironmentDiffuseScale">0</float>
			<float name="EnvironmentSpecularScale">0</float>
			<float name="ExposureCompensation">0</float>
			<Color3 name="FogColor">
				<R>0.75</R>
				<G>0.75</G>
				<B>0.75</B>
			</Color3>
			<float name="FogEnd">100000</float>
			<float name="FogStart">0</float>
			<float name="GeographicLatitude">41.7332993</float>
			<bool name="GlobalShadows">true</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<token name="LightingStyle">0</token>
			<string name="Name">Lighting</string>
			<Color3 name="OutdoorAmbient">
				<R>0.5</R>
				<G>0.5</G>
				<B>0.5</B>
			</Color3>
			<bool name="Outlines">false</bool>
			<bool name="PrioritizeLightingQuality">true</bool>
			<float name="ShadowSoftness">0.5</float>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<token name="Technology">1</token>
			<string name="TimeOfDay">14:00:00</string>
			<UniqueId name="UniqueId">18ccb430d92583bb087716840000038d</UniqueId>
		</Properties>
	</Item>
	<Item class="VideoService" referent="RBX84FE2CE7180A462EB7901FA235E94B33">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">VideoService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb08771684000003af</UniqueId>
		</Properties>
	</Item>
	<Item class="LodDataService" referent="RBX25216D8A902140B4868F58912D95CC5B">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">LodDataService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb08771684000003b0</UniqueId>
		</Properties>
	</Item>
	<Item class="TestService" referent="RBX8078CA643465401FB24C1EE24B2EB927">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<bool name="AutoRuns">true</bool>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<string name="Description"></string>
			<bool name="ExecuteWithStudioRun">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<bool name="IsPhysicsEnvironmentalThrottled">true</bool>
			<bool name="IsSleepAllowed">true</bool>
			<string name="Name">TestService</string>
			<int name="NumberOfPlayers">0</int>
			<double name="SimulateSecondsLag">0</double>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<bool name="ThrottlePhysicsToRealtime">true</bool>
			<double name="Timeout">10</double>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400005aa2</UniqueId>
		</Properties>
	</Item>
	<Item class="UGCAvatarService" referent="RBX319DFF0FF388405BAB9649B7F2631CCA">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">UGCAvatarService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400005aa6</UniqueId>
		</Properties>
	</Item>
	<Item class="VirtualInputManager" referent="RBX8AED7D52DC0D4527BB09622D3D9A7927">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<string name="Name">VirtualInputManager</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400005aad</UniqueId>
		</Properties>
	</Item>
	<Item class="ProximityPromptService" referent="RBX9E38B20C1F0248F89CAD0CF7B34B7E03">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<SecurityCapabilities name="Capabilities">0</SecurityCapabilities>
			<bool name="DefinesCapabilities">false</bool>
			<bool name="Enabled">true</bool>
			<UniqueId name="HistoryId">00000000000000000000000000000000</UniqueId>
			<int name="MaxPromptsVisible">16</int>
			<string name="Name">ProximityPromptService</string>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<UniqueId name="UniqueId">18ccb430d92583bb0877168400005c08</UniqueId>
		</Properties>
	</Item>
	<SharedStrings>
		<SharedString md5="yuZpQdnvvUBOTYh1jqZ2cA=="></SharedString>
	</SharedStrings>
</roblox>