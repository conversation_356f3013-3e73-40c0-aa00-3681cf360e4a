<roblox version="4">
  <Item class="Lighting" referent="0">
    <Properties>
      <string name="Name">Lighting</string>
      <Color3 name="Ambient">
        <R>0</R>
        <G>0</G>
        <B>0</B>
      </Color3>
      <float name="Brightness">2</float>
      <bool name="GlobalShadows">true</bool>
      <bool name="Outlines">false</bool>
      <token name="Technology">1</token>
    </Properties>
  </Item>
  <Item class="ReplicatedStorage" referent="1">
    <Properties>
      <string name="Name">ReplicatedStorage</string>
    </Properties>
    <Item class="Folder" referent="2">
      <Properties>
        <string name="Name">Shared</string>
      </Properties>
      <Item class="ModuleScript" referent="3">
        <Properties>
          <string name="Name">GameConfig</string>
          <string name="Source"><![CDATA[-- Geteilte Konfiguration zwischen Server und Client
-- Diese Datei kann sowohl vom Server als auch vom Client verwendet werden

local GameConfig = {}

-- Spiel-Einstellungen
GameConfig.GAME_NAME = "Mein Roblox Spiel"
GameConfig.VERSION = "1.0.0"

-- Chat-Einstellungen
GameConfig.MAX_MESSAGE_LENGTH = 100
GameConfig.CHAT_COOLDOWN = 1 -- Sekunden zwischen Nachrichten

-- Spieler-Einstellungen
GameConfig.DEFAULT_WALKSPEED = 16
GameConfig.DEFAULT_JUMPPOWER = 50

-- Farben
GameConfig.COLORS = {
    PRIMARY = Color3.new(0.2, 0.6, 1),
    SECONDARY = Color3.new(1, 0.8, 0.2),
    SUCCESS = Color3.new(0.2, 0.8, 0.2),
    ERROR = Color3.new(0.8, 0.2, 0.2),
    WARNING = Color3.new(1, 0.6, 0.2)
}

-- Utility-Funktionen
function GameConfig.formatTime(seconds)
    local minutes = math.floor(seconds / 60)
    local remainingSeconds = seconds % 60
    return string.format("%02d:%02d", minutes, remainingSeconds)
end

function GameConfig.isValidMessage(message)
    return type(message) == "string" 
        and string.len(message) > 0 
        and string.len(message) <= GameConfig.MAX_MESSAGE_LENGTH
end

return GameConfig
]]></string>
        </Properties>
      </Item>
      <Item class="ModuleScript" referent="4">
        <Properties>
          <string name="Name">Hello</string>
          <string name="Source">return function()
	print("Hello, world!")
end</string>
        </Properties>
      </Item>
    </Item>
  </Item>
  <Item class="ServerScriptService" referent="5">
    <Properties>
      <string name="Name">ServerScriptService</string>
    </Properties>
    <Item class="Script" referent="6">
      <Properties>
        <string name="Name">Server</string>
        <token name="RunContext">0</token>
        <string name="Source">print("Hello world, from server!")</string>
      </Properties>
    </Item>
  </Item>
  <Item class="SoundService" referent="7">
    <Properties>
      <string name="Name">SoundService</string>
      <bool name="RespectFilteringEnabled">true</bool>
    </Properties>
  </Item>
  <Item class="StarterPlayer" referent="8">
    <Properties>
      <string name="Name">StarterPlayer</string>
    </Properties>
    <Item class="StarterPlayerScripts" referent="9">
      <Properties>
        <string name="Name">StarterPlayerScripts</string>
      </Properties>
      <Item class="LocalScript" referent="10">
        <Properties>
          <string name="Name">Client</string>
          <string name="Source">print("Hello world, from client!")</string>
        </Properties>
      </Item>
    </Item>
  </Item>
  <Item class="Workspace" referent="11">
    <Properties>
      <string name="Name">Workspace</string>
      <bool name="FilteringEnabled">true</bool>
      <bool name="NeedsPivotMigration">false</bool>
    </Properties>
    <Item class="Part" referent="12">
      <Properties>
        <string name="Name">Baseplate</string>
        <bool name="Anchored">true</bool>
        <Color3uint8 name="Color3uint8">6512483</Color3uint8>
        <bool name="Locked">true</bool>
        <Vector3 name="Position">
          <X>0</X>
          <Y>-10</Y>
          <Z>0</Z>
        </Vector3>
        <Vector3 name="size">
          <X>512</X>
          <Y>20</Y>
          <Z>512</Z>
        </Vector3>
      </Properties>
    </Item>
  </Item>
</roblox>