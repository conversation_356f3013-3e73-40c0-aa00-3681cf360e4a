-- Client-side Hauptskript
-- <PERSON><PERSON> Skript läuft nur auf dem Client (Spieler)

print("💻 Client gestartet!")
print("🔥 VSCode → Roblox Studio Synchronisation AKTIV!")

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local StarterGui = game:GetService("StarterGui")
local TweenService = game:GetService("TweenService")

-- Lokaler Spieler
local player = Players.LocalPlayer

-- Warten auf das RemoteEvent
local remoteEvent = ReplicatedStorage:WaitForChild("PlayerMessage")

-- GUI erstellen
local screenGui = Instance.new("ScreenGui")
screenGui.Name = "VSCodeTestGui"
screenGui.Parent = player:WaitForChild("PlayerGui")

-- 🎯 HAUPT-MENÜ ERSTELLEN
local mainFrame = Instance.new("Frame")
mainFrame.Size = UDim2.new(0.6, 0, 0.7, 0)
mainFrame.Position = UDim2.new(0.2, 0, 0.15, 0)
mainFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
mainFrame.BorderSizePixel = 0
mainFrame.Parent = screenGui

-- Abgerundete Ecken
local corner = Instance.new("UICorner")
corner.CornerRadius = UDim.new(0, 15)
corner.Parent = mainFrame

-- Titel
local titleLabel = Instance.new("TextLabel")
titleLabel.Size = UDim2.new(1, 0, 0.15, 0)
titleLabel.Position = UDim2.new(0, 0, 0, 0)
titleLabel.BackgroundColor3 = Color3.new(0.2, 0.4, 0.8)
titleLabel.Text = "🎉 ROJO IST JETZT VERBUNDEN! 🎉"
titleLabel.TextColor3 = Color3.new(1, 1, 1)
titleLabel.TextScaled = true
titleLabel.Font = Enum.Font.GothamBold
titleLabel.Parent = mainFrame

-- Titel-Ecken
local titleCorner = Instance.new("UICorner")
titleCorner.CornerRadius = UDim.new(0, 15)
titleCorner.Parent = titleLabel

-- Status-Anzeige
local statusLabel = Instance.new("TextLabel")
statusLabel.Size = UDim2.new(0.9, 0, 0.1, 0)
statusLabel.Position = UDim2.new(0.05, 0, 0.2, 0)
statusLabel.BackgroundTransparency = 1
statusLabel.Text = "✅ Synchronisation funktioniert perfekt!"
statusLabel.TextColor3 = Color3.new(0.2, 0.8, 0.2)
statusLabel.TextScaled = true
statusLabel.Font = Enum.Font.Gotham
statusLabel.Parent = mainFrame

-- Test-Buttons erstellen
local buttonFrame = Instance.new("Frame")
buttonFrame.Size = UDim2.new(0.9, 0, 0.4, 0)
buttonFrame.Position = UDim2.new(0.05, 0, 0.35, 0)
buttonFrame.BackgroundTransparency = 1
buttonFrame.Parent = mainFrame

-- Button 1: Farbe ändern
local colorButton = Instance.new("TextButton")
colorButton.Size = UDim2.new(0.45, 0, 0.3, 0)
colorButton.Position = UDim2.new(0.025, 0, 0.1, 0)
colorButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
colorButton.Text = "🌈 MAGISCHE FARBEN!"
colorButton.TextColor3 = Color3.new(1, 1, 1)
colorButton.TextScaled = true
colorButton.Font = Enum.Font.Gotham
colorButton.Parent = buttonFrame

local colorCorner = Instance.new("UICorner")
colorCorner.CornerRadius = UDim.new(0, 10)
colorCorner.Parent = colorButton

-- Button 2: Nachricht senden
local messageButton = Instance.new("TextButton")
messageButton.Size = UDim2.new(0.45, 0, 0.3, 0)
messageButton.Position = UDim2.new(0.525, 0, 0.1, 0)
messageButton.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
messageButton.Text = "💬 Test-Nachricht"
messageButton.TextColor3 = Color3.new(1, 1, 1)
messageButton.TextScaled = true
messageButton.Font = Enum.Font.Gotham
messageButton.Parent = buttonFrame

local messageCorner = Instance.new("UICorner")
messageCorner.CornerRadius = UDim.new(0, 10)
messageCorner.Parent = messageButton

-- Button 3: Schließen
local closeButton = Instance.new("TextButton")
closeButton.Size = UDim2.new(0.2, 0, 0.15, 0)
closeButton.Position = UDim2.new(0.4, 0, 0.8, 0)
closeButton.BackgroundColor3 = Color3.new(0.6, 0.6, 0.6)
closeButton.Text = "❌ Schließen"
closeButton.TextColor3 = Color3.new(1, 1, 1)
closeButton.TextScaled = true
closeButton.Font = Enum.Font.Gotham
closeButton.Parent = mainFrame

local closeCorner = Instance.new("UICorner")
closeCorner.CornerRadius = UDim.new(0, 10)
closeCorner.Parent = closeButton

-- Info-Text
local infoLabel = Instance.new("TextLabel")
infoLabel.Size = UDim2.new(0.9, 0, 0.15, 0)
infoLabel.Position = UDim2.new(0.05, 0, 0.6, 0)
infoLabel.BackgroundTransparency = 1
infoLabel.Text = "Wenn du dieses Menü siehst, funktioniert die VSCode-Synchronisation! 🎉\nÄndere den Code in VSCode und sieh die Änderungen hier!"
infoLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
infoLabel.TextScaled = true
infoLabel.TextWrapped = true
infoLabel.Font = Enum.Font.Gotham
infoLabel.Parent = mainFrame

-- 🎯 BUTTON-FUNKTIONALITÄTEN

-- Farb-Animation für den Titel
local function animateTitle()
    local colors = {
        Color3.new(0.2, 0.4, 0.8),
        Color3.new(0.8, 0.2, 0.4),
        Color3.new(0.4, 0.8, 0.2),
        Color3.new(0.8, 0.6, 0.2)
    }

    local currentColor = 1
    titleLabel.BackgroundColor3 = colors[currentColor]
    currentColor = currentColor % #colors + 1
end

-- Button-Events
colorButton.MouseButton1Click:Connect(function()
    animateTitle()
    statusLabel.Text = "🎨 Farbe geändert! VSCode → Studio funktioniert!"
    statusLabel.TextColor3 = Color3.new(0.8, 0.6, 0.2)
end)

messageButton.MouseButton1Click:Connect(function()
    remoteEvent:FireServer("🚀 Test-Nachricht von VSCode-Synchronisation!")
    statusLabel.Text = "💬 Nachricht gesendet! Server-Kommunikation aktiv!"
    statusLabel.TextColor3 = Color3.new(0.2, 0.8, 0.8)
end)

closeButton.MouseButton1Click:Connect(function()
    -- Fade-out Animation
    local fadeInfo = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
    local fadeTween = TweenService:Create(mainFrame, fadeInfo, {BackgroundTransparency = 1})

    fadeTween:Play()
    fadeTween.Completed:Connect(function()
        screenGui:Destroy()
    end)
end)

-- Automatische Willkommensnachricht
wait(1)
statusLabel.Text = "🎉 VSCode-Entwicklung läuft! Ändere den Code und sieh die Magie!"

-- Server-Nachrichten empfangen
remoteEvent.OnClientEvent:Connect(function(message)
    print("📨 Server-Nachricht:", message)

    -- Kurz den Status aktualisieren
    local originalText = statusLabel.Text
    statusLabel.Text = "📨 " .. message
    statusLabel.TextColor3 = Color3.new(0.8, 0.8, 0.2)

    wait(3)
    statusLabel.Text = originalText
    statusLabel.TextColor3 = Color3.new(0.2, 0.8, 0.2)
end)

-- Tastatur-Shortcut: ESC zum Schließen
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end

    if input.KeyCode == Enum.KeyCode.Escape then
        if screenGui and screenGui.Parent then
            screenGui:Destroy()
        end
    end
end)
