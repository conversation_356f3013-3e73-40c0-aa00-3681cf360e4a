-- SUPER EINFACHES TEST-SKRIPT
print("� VSCODE → ROBLOX LIVE-SYNC AKTIV! 🔥")

local Players = game:GetService("Players")
local player = Players.LocalPlayer

-- Warten bis PlayerGui geladen ist
player:WaitForChild("PlayerGui")
print("🚀 LIVE-SYNC TEST - AUTOMATISCHE ÄNDERUNG! 🚀")
print("ZWEITE ZEILE - LIVE-SYNC TEST!")
print("DRITTE ZEILE - FUNKTIONIERT ES?")

wait(1) -- <PERSON><PERSON> warten

-- SUPER EINFACHES GROSSES FENSTER ERSTELLEN
print("🎨 GUI wird erstellt...")

local screenGui = Instance.new("ScreenGui")
screenGui.Name = "TestGui"
screenGui.Parent = player.PlayerGui

-- MODERNES UI ERSTELLEN
local mainFrame = Instance.new("Frame")
mainFrame.Size = UDim2.new(0.6, 0, 0.7, 0)  -- <PERSON><PERSON><PERSON>, eleganteres Fen<PERSON>
mainFrame.Position = UDim2.new(0.2, 0, 0.15, 0)  -- Zentriert
mainFrame.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)  -- Dunkles Blau-Grau
mainFrame.BorderSizePixel = 0
mainFrame.Parent = screenGui

-- Abgerundete Ecken
local mainCorner = Instance.new("UICorner")
mainCorner.CornerRadius = UDim.new(0, 20)
mainCorner.Parent = mainFrame

-- Schatten-Effekt
local shadow = Instance.new("Frame")
shadow.Size = UDim2.new(1, 10, 1, 10)
shadow.Position = UDim2.new(0, 5, 0, 5)
shadow.BackgroundColor3 = Color3.new(0, 0, 0)
shadow.BackgroundTransparency = 0.7
shadow.ZIndex = mainFrame.ZIndex - 1
shadow.Parent = screenGui

local shadowCorner = Instance.new("UICorner")
shadowCorner.CornerRadius = UDim.new(0, 20)
shadowCorner.Parent = shadow

-- Header mit Gradient
local header = Instance.new("Frame")
header.Size = UDim2.new(1, 0, 0.2, 0)
header.Position = UDim2.new(0, 0, 0, 0)
header.BackgroundColor3 = Color3.new(0.2, 0.4, 0.8)
header.BorderSizePixel = 0
header.Parent = mainFrame

local headerCorner = Instance.new("UICorner")
headerCorner.CornerRadius = UDim.new(0, 20)
headerCorner.Parent = header

-- Gradient für Header
local gradient = Instance.new("UIGradient")
gradient.Color = ColorSequence.new{
    ColorSequenceKeypoint.new(0, Color3.new(0.3, 0.6, 1)),
    ColorSequenceKeypoint.new(1, Color3.new(0.1, 0.3, 0.7))
}
gradient.Rotation = 45
gradient.Parent = header

-- Titel
local titleLabel = Instance.new("TextLabel")
titleLabel.Size = UDim2.new(0.8, 0, 1, 0)
titleLabel.Position = UDim2.new(0.1, 0, 0, 0)
titleLabel.BackgroundTransparency = 1
titleLabel.Text = "� VSCode Live-Sync"
titleLabel.TextColor3 = Color3.new(1, 1, 1)
titleLabel.TextScaled = true
titleLabel.Font = Enum.Font.GothamBold
titleLabel.Parent = header

-- Content-Bereich
local contentFrame = Instance.new("Frame")
contentFrame.Size = UDim2.new(0.9, 0, 0.7, 0)
contentFrame.Position = UDim2.new(0.05, 0, 0.25, 0)
contentFrame.BackgroundTransparency = 1
contentFrame.Parent = mainFrame

-- Status-Anzeige mit Icon
local statusFrame = Instance.new("Frame")
statusFrame.Size = UDim2.new(1, 0, 0.3, 0)
statusFrame.Position = UDim2.new(0, 0, 0, 0)
statusFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
statusFrame.BorderSizePixel = 0
statusFrame.Parent = contentFrame

local statusCorner = Instance.new("UICorner")
statusCorner.CornerRadius = UDim.new(0, 15)
statusCorner.Parent = statusFrame

local statusIcon = Instance.new("TextLabel")
statusIcon.Size = UDim2.new(0.2, 0, 1, 0)
statusIcon.Position = UDim2.new(0, 0, 0, 0)
statusIcon.BackgroundTransparency = 1
statusIcon.Text = "✅"
statusIcon.TextColor3 = Color3.new(0.3, 0.8, 0.3)
statusIcon.TextScaled = true
statusIcon.Font = Enum.Font.GothamBold
statusIcon.Parent = statusFrame

local statusText = Instance.new("TextLabel")
statusText.Size = UDim2.new(0.75, 0, 1, 0)
statusText.Position = UDim2.new(0.2, 0, 0, 0)
statusText.BackgroundTransparency = 1
statusText.Text = "Live-Sync Aktiv! VSCode ↔ Studio"
statusText.TextColor3 = Color3.new(0.9, 0.9, 0.9)
statusText.TextScaled = true
statusText.Font = Enum.Font.Gotham
statusText.TextXAlignment = Enum.TextXAlignment.Left
statusText.Parent = statusFrame

-- Moderne Buttons
local buttonContainer = Instance.new("Frame")
buttonContainer.Size = UDim2.new(1, 0, 0.6, 0)
buttonContainer.Position = UDim2.new(0, 0, 0.35, 0)
buttonContainer.BackgroundTransparency = 1
buttonContainer.Parent = contentFrame

-- Button 1: Test-Nachricht
local testButton = Instance.new("TextButton")
testButton.Size = UDim2.new(0.45, 0, 0.4, 0)
testButton.Position = UDim2.new(0, 0, 0, 0)
testButton.BackgroundColor3 = Color3.new(0.2, 0.6, 0.9)
testButton.BorderSizePixel = 0
testButton.Text = "💬 Test-Chat"
testButton.TextColor3 = Color3.new(1, 1, 1)
testButton.TextScaled = true
testButton.Font = Enum.Font.GothamBold
testButton.Parent = buttonContainer

local testCorner = Instance.new("UICorner")
testCorner.CornerRadius = UDim.new(0, 12)
testCorner.Parent = testButton

-- Button 2: Farbe ändern
local colorButton = Instance.new("TextButton")
colorButton.Size = UDim2.new(0.45, 0, 0.4, 0)
colorButton.Position = UDim2.new(0.55, 0, 0, 0)
colorButton.BackgroundColor3 = Color3.new(0.8, 0.3, 0.6)
colorButton.BorderSizePixel = 0
colorButton.Text = "🎨 Farbe"
colorButton.TextColor3 = Color3.new(1, 1, 1)
colorButton.TextScaled = true
colorButton.Font = Enum.Font.GothamBold
colorButton.Parent = buttonContainer

local colorCorner = Instance.new("UICorner")
colorCorner.CornerRadius = UDim.new(0, 12)
colorCorner.Parent = colorButton

-- Schließen-Button
local closeButton = Instance.new("TextButton")
closeButton.Size = UDim2.new(0.3, 0, 0.3, 0)
closeButton.Position = UDim2.new(0.35, 0, 0.55, 0)
closeButton.BackgroundColor3 = Color3.new(0.6, 0.2, 0.2)
closeButton.BorderSizePixel = 0
closeButton.Text = "❌ Schließen"
closeButton.TextColor3 = Color3.new(1, 1, 1)
closeButton.TextScaled = true
closeButton.Font = Enum.Font.Gotham
closeButton.Parent = buttonContainer

local closeCorner = Instance.new("UICorner")
closeCorner.CornerRadius = UDim.new(0, 12)
closeCorner.Parent = closeButton

print("✅ Modernes UI erstellt!")

-- CHAT-NACHRICHT SENDEN
local StarterGui = game:GetService("StarterGui")
StarterGui:SetCore("ChatMakeSystemMessage", {
    Text = "� Neues modernes UI geladen!";
    Color = Color3.new(0.3, 0.6, 1);  -- Blau
    Font = Enum.Font.GothamBold;
    FontSize = Enum.FontSize.Size18;
})

-- Titel-Ecken
local titleCorner = Instance.new("UICorner")
titleCorner.CornerRadius = UDim.new(0, 15)
titleCorner.Parent = titleLabel

-- Status-Anzeige
local statusLabel = Instance.new("TextLabel")
statusLabel.Size = UDim2.new(0.9, 0, 0.1, 0)
statusLabel.Position = UDim2.new(0.05, 0, 0.2, 0)
statusLabel.BackgroundTransparency = 1
statusLabel.Text = "🎉 WENN DU DAS SIEHST, FUNKTIONIERT ALLES! 🎉"
statusLabel.TextColor3 = Color3.new(0.2, 0.8, 0.2)
statusLabel.TextScaled = true
statusLabel.Font = Enum.Font.Gotham
statusLabel.Parent = mainFrame

-- Test-Buttons erstellen
local buttonFrame = Instance.new("Frame")
buttonFrame.Size = UDim2.new(0.9, 0, 0.4, 0)
buttonFrame.Position = UDim2.new(0.05, 0, 0.35, 0)
buttonFrame.BackgroundTransparency = 1
buttonFrame.Parent = mainFrame

-- Button 1: Farbe ändern
local colorButton = Instance.new("TextButton")
colorButton.Size = UDim2.new(0.45, 0, 0.3, 0)
colorButton.Position = UDim2.new(0.025, 0, 0.1, 0)
colorButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
colorButton.Text = "✨ LIVE-SYNC AKTIV! ✨"
colorButton.TextColor3 = Color3.new(1, 1, 1)
colorButton.TextScaled = true
colorButton.Font = Enum.Font.Gotham
colorButton.Parent = buttonFrame

local colorCorner = Instance.new("UICorner")
colorCorner.CornerRadius = UDim.new(0, 10)
colorCorner.Parent = colorButton

-- Button 2: Nachricht senden
local messageButton = Instance.new("TextButton")
messageButton.Size = UDim2.new(0.45, 0, 0.3, 0)
messageButton.Position = UDim2.new(0.525, 0, 0.1, 0)
messageButton.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
messageButton.Text = "💬 Test-Nachricht"
messageButton.TextColor3 = Color3.new(1, 1, 1)
messageButton.TextScaled = true
messageButton.Font = Enum.Font.Gotham
messageButton.Parent = buttonFrame

local messageCorner = Instance.new("UICorner")
messageCorner.CornerRadius = UDim.new(0, 10)
messageCorner.Parent = messageButton

-- Button 3: Schließen
local closeButton = Instance.new("TextButton")
closeButton.Size = UDim2.new(0.2, 0, 0.15, 0)
closeButton.Position = UDim2.new(0.4, 0, 0.8, 0)
closeButton.BackgroundColor3 = Color3.new(0.6, 0.6, 0.6)
closeButton.Text = "❌ Schließen"
closeButton.TextColor3 = Color3.new(1, 1, 1)
closeButton.TextScaled = true
closeButton.Font = Enum.Font.Gotham
closeButton.Parent = mainFrame

local closeCorner = Instance.new("UICorner")
closeCorner.CornerRadius = UDim.new(0, 10)
closeCorner.Parent = closeButton

-- Info-Text
local infoLabel = Instance.new("TextLabel")
infoLabel.Size = UDim2.new(0.9, 0, 0.15, 0)
infoLabel.Position = UDim2.new(0.05, 0, 0.6, 0)
infoLabel.BackgroundTransparency = 1
infoLabel.Text = "Wenn du dieses Menü siehst, funktioniert die VSCode-Synchronisation! 🎉\nÄndere den Code in VSCode und sieh die Änderungen hier!"
infoLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
infoLabel.TextScaled = true
infoLabel.TextWrapped = true
infoLabel.Font = Enum.Font.Gotham
infoLabel.Parent = mainFrame

-- 🎯 BUTTON-FUNKTIONALITÄTEN

-- Farb-Animation für den Titel
local function animateTitle()
    local colors = {
        Color3.new(0.2, 0.4, 0.8),
        Color3.new(0.8, 0.2, 0.4),
        Color3.new(0.4, 0.8, 0.2),
        Color3.new(0.8, 0.6, 0.2)
    }

    local currentColor = 1
    titleLabel.BackgroundColor3 = colors[currentColor]
    currentColor = currentColor % #colors + 1
end

-- Button-Funktionalitäten
local TweenService = game:GetService("TweenService")

-- Farb-Animation für Header
local colors = {
    Color3.new(0.2, 0.4, 0.8),
    Color3.new(0.8, 0.2, 0.4),
    Color3.new(0.4, 0.8, 0.2),
    Color3.new(0.8, 0.6, 0.2)
}
local currentColor = 1

-- Button-Events
testButton.MouseButton1Click:Connect(function()
    StarterGui:SetCore("ChatMakeSystemMessage", {
        Text = "💬 Test-Button geklickt! Live-Sync funktioniert!";
        Color = Color3.new(0.2, 0.6, 0.9);
        Font = Enum.Font.GothamBold;
        FontSize = Enum.FontSize.Size16;
    })
    statusText.Text = "💬 Test-Nachricht gesendet!"
    statusIcon.Text = "💬"
end)

colorButton.MouseButton1Click:Connect(function()
    header.BackgroundColor3 = colors[currentColor]
    currentColor = currentColor % #colors + 1
    statusText.Text = "🎨 Header-Farbe geändert!"
    statusIcon.Text = "🎨"
end)

closeButton.MouseButton1Click:Connect(function()
    -- Smooth Fade-out Animation
    local fadeInfo = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
    local fadeTween = TweenService:Create(mainFrame, fadeInfo, {BackgroundTransparency = 1})
    local shadowTween = TweenService:Create(shadow, fadeInfo, {BackgroundTransparency = 1})

    fadeTween:Play()
    shadowTween:Play()
    fadeTween.Completed:Connect(function()
        screenGui:Destroy()
    end)
end)

-- Automatische Willkommensnachricht
wait(1)
statusLabel.Text = "🎉 VSCode-Entwicklung läuft! Ändere den Code und sieh die Magie!"

-- Server-Nachrichten empfangen
remoteEvent.OnClientEvent:Connect(function(message)
    print("📨 Server-Nachricht:", message)

    -- Kurz den Status aktualisieren
    local originalText = statusLabel.Text
    statusLabel.Text = "📨 " .. message
    statusLabel.TextColor3 = Color3.new(0.8, 0.8, 0.2)

    wait(3)
    statusLabel.Text = originalText
    statusLabel.TextColor3 = Color3.new(0.2, 0.8, 0.2)
end)

-- Tastatur-Shortcut: ESC zum Schließen
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end

    if input.KeyCode == Enum.KeyCode.Escape then
        if screenGui and screenGui.Parent then
            screenGui:Destroy()
        end
    end
end)
