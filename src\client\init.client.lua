-- Client-side Hauptskript
-- <PERSON>ses Skript läuft nur auf dem Client (Spieler)

print("💻 Client gestartet!")

-- Services
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local StarterGui = game:GetService("StarterGui")

-- Lokaler Spieler
local player = Players.LocalPlayer

-- Warten auf das RemoteEvent
local remoteEvent = ReplicatedStorage:WaitForChild("PlayerMessage")

-- GUI erstellen
local screenGui = Instance.new("ScreenGui")
screenGui.Name = "ChatGui"
screenGui.Parent = player:WaitFor<PERSON>hild("PlayerGui")

-- Chat-Anzeige
local chatFrame = Instance.new("Frame")
chatFrame.Size = UDim2.new(0.4, 0, 0.3, 0)
chatFrame.Position = UDim2.new(0.05, 0, 0.65, 0)
chatFrame.BackgroundColor3 = Color3.new(0, 0, 0)
chatFrame.BackgroundTransparency = 0.3
chatFrame.BorderSizePixel = 0
chatFrame.Parent = screenGui

local chatLabel = Instance.new("TextLabel")
chatLabel.Size = UDim2.new(1, 0, 1, 0)
chatLabel.Position = UDim2.new(0, 0, 0, 0)
chatLabel.BackgroundTransparency = 1
chatLabel.Text = "Willkommen im Spiel!\nDrücke ENTER um zu chatten."
chatLabel.TextColor3 = Color3.new(1, 1, 1)
chatLabel.TextScaled = true
chatLabel.TextWrapped = true
chatLabel.TextXAlignment = Enum.TextXAlignment.Left
chatLabel.TextYAlignment = Enum.TextYAlignment.Top
chatLabel.Parent = chatFrame

-- Chat-Input
local chatInput = Instance.new("TextBox")
chatInput.Size = UDim2.new(0.4, 0, 0.05, 0)
chatInput.Position = UDim2.new(0.05, 0, 0.9, 0)
chatInput.BackgroundColor3 = Color3.new(1, 1, 1)
chatInput.Text = ""
chatInput.PlaceholderText = "Nachricht eingeben..."
chatInput.TextScaled = true
chatInput.Visible = false
chatInput.Parent = screenGui

-- Chat-Nachrichten anzeigen
remoteEvent.OnClientEvent:Connect(function(message)
    local currentText = chatLabel.Text
    chatLabel.Text = currentText .. "\n" .. message
end)

-- Chat-Input aktivieren
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end
    
    if input.KeyCode == Enum.KeyCode.Return then
        chatInput.Visible = true
        chatInput:CaptureFocus()
    end
end)

-- Nachricht senden
chatInput.FocusLost:Connect(function(enterPressed)
    if enterPressed and chatInput.Text ~= "" then
        remoteEvent:FireServer(chatInput.Text)
        chatInput.Text = ""
    end
    chatInput.Visible = false
end)
