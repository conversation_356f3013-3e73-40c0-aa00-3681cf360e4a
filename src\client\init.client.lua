-- SUPER EINFACHES TEST-SKRIPT
print("� VSCODE → <PERSON>O<PERSON><PERSON> LIVE-SYNC AKTIV! 🔥")

local Players = game:GetService("Players")
local player = Players.LocalPlayer

-- Warten bis PlayerGui geladen ist
player:WaitForChild("PlayerGui")
print("SUPER NEUE AENDERUNG - SIEHST DU MICH?")
print("ZWEITE ZEILE - LIVE-SYNC TEST!")
print("DRITTE ZEILE - FUNKTIONIERT ES?")

wait(1) -- <PERSON><PERSON> warten

-- SUPER EINFACHES GROSSES FENSTER ERSTELLEN
print("🎨 GUI wird erstellt...")

local screenGui = Instance.new("ScreenGui")
screenGui.Name = "TestGui"
screenGui.Parent = player.PlayerGui

-- RIESIGES ROTES FENSTER (unmöglich zu übersehen!)
local frame = Instance.new("Frame")
frame.Size = UDim2.new(0.9, 0, 0.9, 0)  -- 90% des Bildschirms!
frame.Position = UDim2.new(0.05, 0, 0.05, 0)  -- Zentriert
frame.BackgroundColor3 = Color3.new(0, 1, 0)  -- GRUEN!
frame.BorderSizePixel = 5
frame.BorderColor3 = Color3.new(1, 1, 0)  -- Gelber Rand
frame.Parent = screenGui

-- RIESIGER TEXT
local label = Instance.new("TextLabel")
label.Size = UDim2.new(1, 0, 1, 0)  -- Ganzes Fenster
label.Position = UDim2.new(0, 0, 0, 0)
label.BackgroundTransparency = 1  -- Transparent
label.Text = "🎉 LIVE-SYNC FUNKTIONIERT PERFEKT! 🎉"
label.TextColor3 = Color3.new(1, 1, 1)  -- Weiß
label.TextScaled = true  -- Text skaliert automatisch
label.Font = Enum.Font.GothamBold
label.Parent = frame

print("✅ GUI erstellt! Du solltest ein RIESIGES ROTES FENSTER sehen!")

-- CHAT-NACHRICHT SENDEN
local StarterGui = game:GetService("StarterGui")
StarterGui:SetCore("ChatMakeSystemMessage", {
    Text = "🚀 LIVE-SYNC AKTIV! Code wurde von VSCode übertragen!";
    Color = Color3.new(0, 1, 0);  -- Grün
    Font = Enum.Font.GothamBold;
    FontSize = Enum.FontSize.Size18;
})

wait(1)

StarterGui:SetCore("ChatMakeSystemMessage", {
    Text = "✨ Jede Änderung in VSCode erscheint sofort hier!";
    Color = Color3.new(1, 0.5, 0);  -- Orange
    Font = Enum.Font.Gotham;
    FontSize = Enum.FontSize.Size14;
})

wait(2)

StarterGui:SetCore("ChatMakeSystemMessage", {
    Text = "🎉 NEUE LIVE-ÄNDERUNG! Das ist echte Live-Entwicklung!";
    Color = Color3.new(1, 0, 1);  -- Magenta
    Font = Enum.Font.GothamBold;
    FontSize = Enum.FontSize.Size18;
})

-- Titel-Ecken
local titleCorner = Instance.new("UICorner")
titleCorner.CornerRadius = UDim.new(0, 15)
titleCorner.Parent = titleLabel

-- Status-Anzeige
local statusLabel = Instance.new("TextLabel")
statusLabel.Size = UDim2.new(0.9, 0, 0.1, 0)
statusLabel.Position = UDim2.new(0.05, 0, 0.2, 0)
statusLabel.BackgroundTransparency = 1
statusLabel.Text = "🎉 WENN DU DAS SIEHST, FUNKTIONIERT ALLES! 🎉"
statusLabel.TextColor3 = Color3.new(0.2, 0.8, 0.2)
statusLabel.TextScaled = true
statusLabel.Font = Enum.Font.Gotham
statusLabel.Parent = mainFrame

-- Test-Buttons erstellen
local buttonFrame = Instance.new("Frame")
buttonFrame.Size = UDim2.new(0.9, 0, 0.4, 0)
buttonFrame.Position = UDim2.new(0.05, 0, 0.35, 0)
buttonFrame.BackgroundTransparency = 1
buttonFrame.Parent = mainFrame

-- Button 1: Farbe ändern
local colorButton = Instance.new("TextButton")
colorButton.Size = UDim2.new(0.45, 0, 0.3, 0)
colorButton.Position = UDim2.new(0.025, 0, 0.1, 0)
colorButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
colorButton.Text = "✨ LIVE-SYNC AKTIV! ✨"
colorButton.TextColor3 = Color3.new(1, 1, 1)
colorButton.TextScaled = true
colorButton.Font = Enum.Font.Gotham
colorButton.Parent = buttonFrame

local colorCorner = Instance.new("UICorner")
colorCorner.CornerRadius = UDim.new(0, 10)
colorCorner.Parent = colorButton

-- Button 2: Nachricht senden
local messageButton = Instance.new("TextButton")
messageButton.Size = UDim2.new(0.45, 0, 0.3, 0)
messageButton.Position = UDim2.new(0.525, 0, 0.1, 0)
messageButton.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
messageButton.Text = "💬 Test-Nachricht"
messageButton.TextColor3 = Color3.new(1, 1, 1)
messageButton.TextScaled = true
messageButton.Font = Enum.Font.Gotham
messageButton.Parent = buttonFrame

local messageCorner = Instance.new("UICorner")
messageCorner.CornerRadius = UDim.new(0, 10)
messageCorner.Parent = messageButton

-- Button 3: Schließen
local closeButton = Instance.new("TextButton")
closeButton.Size = UDim2.new(0.2, 0, 0.15, 0)
closeButton.Position = UDim2.new(0.4, 0, 0.8, 0)
closeButton.BackgroundColor3 = Color3.new(0.6, 0.6, 0.6)
closeButton.Text = "❌ Schließen"
closeButton.TextColor3 = Color3.new(1, 1, 1)
closeButton.TextScaled = true
closeButton.Font = Enum.Font.Gotham
closeButton.Parent = mainFrame

local closeCorner = Instance.new("UICorner")
closeCorner.CornerRadius = UDim.new(0, 10)
closeCorner.Parent = closeButton

-- Info-Text
local infoLabel = Instance.new("TextLabel")
infoLabel.Size = UDim2.new(0.9, 0, 0.15, 0)
infoLabel.Position = UDim2.new(0.05, 0, 0.6, 0)
infoLabel.BackgroundTransparency = 1
infoLabel.Text = "Wenn du dieses Menü siehst, funktioniert die VSCode-Synchronisation! 🎉\nÄndere den Code in VSCode und sieh die Änderungen hier!"
infoLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
infoLabel.TextScaled = true
infoLabel.TextWrapped = true
infoLabel.Font = Enum.Font.Gotham
infoLabel.Parent = mainFrame

-- 🎯 BUTTON-FUNKTIONALITÄTEN

-- Farb-Animation für den Titel
local function animateTitle()
    local colors = {
        Color3.new(0.2, 0.4, 0.8),
        Color3.new(0.8, 0.2, 0.4),
        Color3.new(0.4, 0.8, 0.2),
        Color3.new(0.8, 0.6, 0.2)
    }

    local currentColor = 1
    titleLabel.BackgroundColor3 = colors[currentColor]
    currentColor = currentColor % #colors + 1
end

-- Button-Events
colorButton.MouseButton1Click:Connect(function()
    animateTitle()
    statusLabel.Text = "🎨 Farbe geändert! VSCode → Studio funktioniert!"
    statusLabel.TextColor3 = Color3.new(0.8, 0.6, 0.2)
end)

messageButton.MouseButton1Click:Connect(function()
    remoteEvent:FireServer("🚀 Test-Nachricht von VSCode-Synchronisation!")
    statusLabel.Text = "💬 Nachricht gesendet! Server-Kommunikation aktiv!"
    statusLabel.TextColor3 = Color3.new(0.2, 0.8, 0.8)
end)

closeButton.MouseButton1Click:Connect(function()
    -- Fade-out Animation
    local fadeInfo = TweenInfo.new(0.5, Enum.EasingStyle.Quad, Enum.EasingDirection.Out)
    local fadeTween = TweenService:Create(mainFrame, fadeInfo, {BackgroundTransparency = 1})

    fadeTween:Play()
    fadeTween.Completed:Connect(function()
        screenGui:Destroy()
    end)
end)

-- Automatische Willkommensnachricht
wait(1)
statusLabel.Text = "🎉 VSCode-Entwicklung läuft! Ändere den Code und sieh die Magie!"

-- Server-Nachrichten empfangen
remoteEvent.OnClientEvent:Connect(function(message)
    print("📨 Server-Nachricht:", message)

    -- Kurz den Status aktualisieren
    local originalText = statusLabel.Text
    statusLabel.Text = "📨 " .. message
    statusLabel.TextColor3 = Color3.new(0.8, 0.8, 0.2)

    wait(3)
    statusLabel.Text = originalText
    statusLabel.TextColor3 = Color3.new(0.2, 0.8, 0.2)
end)

-- Tastatur-Shortcut: ESC zum Schließen
UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then return end

    if input.KeyCode == Enum.KeyCode.Escape then
        if screenGui and screenGui.Parent then
            screenGui:Destroy()
        end
    end
end)
