-- CLIENT SCRIPT FÜR ROJO
print("🚀 VSCODE → ROBLOX LIVE-SYNC AKTIV! 🔥")

local Players = game:GetService("Players")
local player = Players.LocalPlayer

-- Warten bis PlayerGui geladen ist
player:WaitForChild("PlayerGui")
print("🔥 NEUE LIVE-ÄNDERUNG! Rojo läuft jetzt perfekt! 🔥")

wait(1) -- <PERSON><PERSON> warten

-- SUPER EINFACHES GROSSES FENSTER ERSTELLEN
print("🎨 GUI wird erstellt...")

local screenGui = Instance.new("ScreenGui")
screenGui.Name = "TestGui"
screenGui.Parent = player.PlayerGui

-- SOFORTIGE LIVE-SYNC CHAT-NACHRICHT
local StarterGui = game:GetService("StarterGui")
StarterGui:SetCore("ChatMakeSystemMessage", {
    Text = "LIVE-SYNC FUNKTIONIERT! Diese Nachricht kommt von VSCode!";
    Color = Color3.new(0, 1, 0);  -- <PERSON><PERSON><PERSON>n
    Font = Enum.Font.GothamBold;
    FontSize = Enum.FontSize.Size18;
})

wait(1)

StarterGui:SetCore("ChatMakeSystemMessage", {
    Text = "Keine F5 nötig - Live-Entwicklung aktiv!";
    Color = Color3.new(1, 0.5, 0);  -- Orange
    Font = Enum.Font.Gotham;
    FontSize = Enum.FontSize.Size16;
})

print("✅ Chat-Nachrichten gesendet!")
